import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Zap, 
  Smartphone, 
  Building, 
  Globe, 
  Shield, 
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';

interface PowerSystem {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  testTypes: string[];
  businessImpact: string;
  complianceReqs: string[];
}

interface PowerSystemSelectorProps {
  onSystemSelect: (system: PowerSystem) => void;
  selectedSystem?: PowerSystem;
}

const powerSystems: PowerSystem[] = [
  {
    id: 'marketing_system',
    name: '电力营销系统2.0',
    description: '客户管理、电费计算、缴费服务等核心营销业务系统',
    icon: <Zap className="h-8 w-8 text-yellow-500" />,
    riskLevel: 'high',
    testTypes: ['业务逻辑测试', 'SQL注入测试', '权限控制测试', '数据保护测试'],
    businessImpact: '影响客户服务、电费收入、客户数据安全',
    complianceReqs: ['等保2.0', '个人信息保护法', '数据安全法']
  },
  {
    id: 'mobile_app',
    name: 'i国网 移动应用',
    description: '面向客户的移动端服务应用，包含缴费、查询等功能',
    icon: <Smartphone className="h-8 w-8 text-blue-500" />,
    riskLevel: 'critical',
    testTypes: ['移动应用安全', '支付安全测试', '通信安全测试', '客户端保护'],
    businessImpact: '直接影响客户体验、支付安全、品牌声誉',
    complianceReqs: ['移动应用安全标准', '支付安全规范', '个人信息保护法']
  },
  {
    id: 'web_portal',
    name: '网上国网门户',
    description: 'Web端客户服务门户，提供在线业务办理服务',
    icon: <Globe className="h-8 w-8 text-green-500" />,
    riskLevel: 'high',
    testTypes: ['Web应用安全', 'XSS测试', 'CSRF测试', '会话管理测试'],
    businessImpact: '影响在线服务可用性、客户数据安全',
    complianceReqs: ['等保2.0', 'Web安全标准', '个人信息保护法']
  },
  {
    id: 'erp_system',
    name: 'ERP企业管理系统',
    description: '企业资源规划系统，管理人财物等核心资源',
    icon: <Building className="h-8 w-8 text-purple-500" />,
    riskLevel: 'high',
    testTypes: ['权限提升测试', '内部威胁检测', '财务数据保护', '审计日志测试'],
    businessImpact: '影响企业运营、财务安全、内部管理',
    complianceReqs: ['等保2.0', '数据安全法', '企业内控制度']
  }
];

const getRiskLevelColor = (level: string) => {
  switch (level) {
    case 'low': return 'bg-green-100 text-green-800';
    case 'medium': return 'bg-yellow-100 text-yellow-800';
    case 'high': return 'bg-orange-100 text-orange-800';
    case 'critical': return 'bg-red-100 text-red-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getRiskLevelIcon = (level: string) => {
  switch (level) {
    case 'low': return <CheckCircle className="h-4 w-4" />;
    case 'medium': return <Clock className="h-4 w-4" />;
    case 'high': return <AlertTriangle className="h-4 w-4" />;
    case 'critical': return <Shield className="h-4 w-4" />;
    default: return <AlertTriangle className="h-4 w-4" />;
  }
};

const PowerSystemSelector: React.FC<PowerSystemSelectorProps> = ({
  onSystemSelect,
  selectedSystem
}) => {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold text-gray-900">
          选择电力企业IT系统
        </h2>
        <p className="text-gray-600">
          选择要进行安全测试的目标系统，AI将基于电力行业特点进行专业化安全评估
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="overview">系统概览</TabsTrigger>
          <TabsTrigger value="details">详细信息</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {powerSystems.map((system) => (
              <Card 
                key={system.id}
                className={`cursor-pointer transition-all hover:shadow-lg ${
                  selectedSystem?.id === system.id 
                    ? 'ring-2 ring-blue-500 bg-blue-50' 
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => onSystemSelect(system)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {system.icon}
                      <div>
                        <CardTitle className="text-lg">{system.name}</CardTitle>
                        <CardDescription className="text-sm">
                          {system.description}
                        </CardDescription>
                      </div>
                    </div>
                    <Badge 
                      className={`${getRiskLevelColor(system.riskLevel)} flex items-center space-x-1`}
                    >
                      {getRiskLevelIcon(system.riskLevel)}
                      <span className="capitalize">{system.riskLevel}</span>
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-2">
                        主要测试类型
                      </h4>
                      <div className="flex flex-wrap gap-1">
                        {system.testTypes.slice(0, 3).map((type, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {type}
                          </Badge>
                        ))}
                        {system.testTypes.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{system.testTypes.length - 3}
                          </Badge>
                        )}
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-1">
                        业务影响
                      </h4>
                      <p className="text-xs text-gray-600">
                        {system.businessImpact}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="details" className="space-y-4">
          {selectedSystem ? (
            <Card>
              <CardHeader>
                <div className="flex items-center space-x-3">
                  {selectedSystem.icon}
                  <div>
                    <CardTitle>{selectedSystem.name}</CardTitle>
                    <CardDescription>{selectedSystem.description}</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-3">安全测试范围</h3>
                  <div className="grid grid-cols-2 gap-2">
                    {selectedSystem.testTypes.map((type, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="text-sm">{type}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-3">合规要求</h3>
                  <div className="space-y-2">
                    {selectedSystem.complianceReqs.map((req, index) => (
                      <Badge key={index} variant="outline" className="mr-2">
                        {req}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold mb-3">业务影响评估</h3>
                  <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded-lg">
                    {selectedSystem.businessImpact}
                  </p>
                </div>

                <div className="pt-4 border-t">
                  <Button 
                    onClick={() => onSystemSelect(selectedSystem)}
                    className="w-full"
                    size="lg"
                  >
                    开始安全测试
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  请选择目标系统
                </h3>
                <p className="text-gray-600">
                  在系统概览中选择要测试的电力企业IT系统
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PowerSystemSelector;
