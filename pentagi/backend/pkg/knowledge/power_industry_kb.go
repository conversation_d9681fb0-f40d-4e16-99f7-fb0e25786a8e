package knowledge

import (
	"context"
	"encoding/json"
	"fmt"
)

// PowerIndustryKnowledgeBase 电力行业知识库
type PowerIndustryKnowledgeBase struct {
	SecurityStandards    map[string]SecurityStandard    `json:"security_standards"`
	ThreatIntelligence  map[string]ThreatInfo          `json:"threat_intelligence"`
	VulnerabilityDB     map[string]VulnerabilityInfo   `json:"vulnerability_db"`
	BusinessLogicRules  map[string]BusinessRule        `json:"business_logic_rules"`
	ComplianceRequirements map[string]ComplianceReq    `json:"compliance_requirements"`
}

// SecurityStandard 安全标准
type SecurityStandard struct {
	Name        string            `json:"name"`
	Version     string            `json:"version"`
	Description string            `json:"description"`
	Requirements []Requirement    `json:"requirements"`
	Applicability []string        `json:"applicability"` // 适用系统类型
}

// Requirement 安全要求
type Requirement struct {
	ID          string   `json:"id"`
	Category    string   `json:"category"`
	Description string   `json:"description"`
	Level       string   `json:"level"` // 基本/增强/高级
	TestMethods []string `json:"test_methods"`
}

// ThreatInfo 威胁信息
type ThreatInfo struct {
	Name            string   `json:"name"`
	Category        string   `json:"category"`
	Description     string   `json:"description"`
	AttackVectors   []string `json:"attack_vectors"`
	TargetSystems   []string `json:"target_systems"`
	BusinessImpact  string   `json:"business_impact"`
	Likelihood      string   `json:"likelihood"`
	Severity        string   `json:"severity"`
}

// VulnerabilityInfo 漏洞信息
type VulnerabilityInfo struct {
	CVE             string   `json:"cve"`
	Name            string   `json:"name"`
	Description     string   `json:"description"`
	CVSS            float64  `json:"cvss"`
	Category        string   `json:"category"`
	AffectedSystems []string `json:"affected_systems"`
	ExploitMethods  []string `json:"exploit_methods"`
	Mitigation      []string `json:"mitigation"`
}

// BusinessRule 业务规则
type BusinessRule struct {
	RuleID      string            `json:"rule_id"`
	SystemType  string            `json:"system_type"`
	Category    string            `json:"category"`
	Description string            `json:"description"`
	Logic       string            `json:"logic"`
	TestCases   []BusinessTestCase `json:"test_cases"`
}

// BusinessTestCase 业务测试用例
type BusinessTestCase struct {
	Name        string `json:"name"`
	Input       string `json:"input"`
	Expected    string `json:"expected"`
	RiskLevel   string `json:"risk_level"`
}

// ComplianceReq 合规要求
type ComplianceReq struct {
	Regulation  string   `json:"regulation"`
	Article     string   `json:"article"`
	Description string   `json:"description"`
	Requirements []string `json:"requirements"`
	Penalties   string   `json:"penalties"`
}

// NewPowerIndustryKnowledgeBase 创建电力行业知识库
func NewPowerIndustryKnowledgeBase() *PowerIndustryKnowledgeBase {
	kb := &PowerIndustryKnowledgeBase{
		SecurityStandards:      make(map[string]SecurityStandard),
		ThreatIntelligence:     make(map[string]ThreatInfo),
		VulnerabilityDB:        make(map[string]VulnerabilityInfo),
		BusinessLogicRules:     make(map[string]BusinessRule),
		ComplianceRequirements: make(map[string]ComplianceReq),
	}
	
	kb.initializeKnowledgeBase()
	return kb
}

// initializeKnowledgeBase 初始化知识库
func (kb *PowerIndustryKnowledgeBase) initializeKnowledgeBase() {
	// 初始化安全标准
	kb.initSecurityStandards()
	
	// 初始化威胁情报
	kb.initThreatIntelligence()
	
	// 初始化漏洞数据库
	kb.initVulnerabilityDB()
	
	// 初始化业务逻辑规则
	kb.initBusinessLogicRules()
	
	// 初始化合规要求
	kb.initComplianceRequirements()
}

// initSecurityStandards 初始化安全标准
func (kb *PowerIndustryKnowledgeBase) initSecurityStandards() {
	// 等保2.0标准
	kb.SecurityStandards["GB/T22239-2019"] = SecurityStandard{
		Name:        "信息安全技术 网络安全等级保护基本要求",
		Version:     "2019",
		Description: "网络安全等级保护基本要求",
		Requirements: []Requirement{
			{
				ID:          "A1.1.1",
				Category:    "身份鉴别",
				Description: "应对登录的用户进行身份标识和鉴别",
				Level:       "基本",
				TestMethods: []string{"弱密码测试", "暴力破解测试", "多因子认证检查"},
			},
			{
				ID:          "A1.2.1",
				Category:    "访问控制",
				Description: "应启用访问控制功能，依据安全策略控制用户对资源的访问",
				Level:       "基本",
				TestMethods: []string{"权限提升测试", "越权访问测试", "访问控制绕过"},
			},
		},
		Applicability: []string{"营销系统", "ERP系统", "移动应用"},
	}
	
	// 电力行业标准
	kb.SecurityStandards["DL/T1071-2007"] = SecurityStandard{
		Name:        "电力信息系统安全等级保护实施指南",
		Version:     "2007",
		Description: "电力行业信息系统安全等级保护实施指南",
		Requirements: []Requirement{
			{
				ID:          "P1.1",
				Category:    "电力业务安全",
				Description: "电力营销系统应确保客户信息和用电数据的安全",
				Level:       "基本",
				TestMethods: []string{"数据泄露测试", "业务逻辑测试"},
			},
		},
		Applicability: []string{"电力营销系统", "电力ERP系统"},
	}
}

// initThreatIntelligence 初始化威胁情报
func (kb *PowerIndustryKnowledgeBase) initThreatIntelligence() {
	kb.ThreatIntelligence["电费计算绕过"] = ThreatInfo{
		Name:        "电费计算逻辑绕过",
		Category:    "业务逻辑攻击",
		Description: "攻击者通过篡改计费参数绕过正常的电费计算逻辑",
		AttackVectors: []string{
			"修改用电量参数",
			"篡改电价类型",
			"绕过阶梯计费规则",
		},
		TargetSystems:  []string{"电力营销系统"},
		BusinessImpact: "可能导致电费计算错误，造成经济损失",
		Likelihood:     "中",
		Severity:       "高",
	}
	
	kb.ThreatIntelligence["移动支付欺诈"] = ThreatInfo{
		Name:        "移动支付欺诈",
		Category:    "支付安全",
		Description: "攻击者通过篡改支付金额或绕过支付验证进行欺诈",
		AttackVectors: []string{
			"支付金额篡改",
			"支付状态绕过",
			"重放攻击",
		},
		TargetSystems:  []string{"i国网app", "网上国网"},
		BusinessImpact: "直接财务损失，影响用户信任",
		Likelihood:     "中",
		Severity:       "极高",
	}
}

// initVulnerabilityDB 初始化漏洞数据库
func (kb *PowerIndustryKnowledgeBase) initVulnerabilityDB() {
	kb.VulnerabilityDB["POWER-001"] = VulnerabilityInfo{
		CVE:         "CVE-2024-POWER-001",
		Name:        "电力营销系统SQL注入",
		Description: "电力营销系统客户查询接口存在SQL注入漏洞",
		CVSS:        8.5,
		Category:    "注入攻击",
		AffectedSystems: []string{"电力营销系统2.0"},
		ExploitMethods: []string{
			"联合查询注入",
			"布尔盲注",
			"时间盲注",
		},
		Mitigation: []string{
			"使用参数化查询",
			"输入验证和过滤",
			"最小权限原则",
		},
	}
}

// initBusinessLogicRules 初始化业务逻辑规则
func (kb *PowerIndustryKnowledgeBase) initBusinessLogicRules() {
	kb.BusinessLogicRules["电费计算"] = BusinessRule{
		RuleID:      "BL-001",
		SystemType:  "电力营销系统",
		Category:    "计费逻辑",
		Description: "阶梯电价计算规则",
		Logic:       "根据用电量区间应用不同电价，累进计算总费用",
		TestCases: []BusinessTestCase{
			{
				Name:      "负数用电量测试",
				Input:     `{"usage": -100}`,
				Expected:  "应拒绝负数用电量",
				RiskLevel: "高",
			},
			{
				Name:      "超大用电量测试",
				Input:     `{"usage": 999999999}`,
				Expected:  "应验证用电量合理性",
				RiskLevel: "中",
			},
		},
	}
}

// initComplianceRequirements 初始化合规要求
func (kb *PowerIndustryKnowledgeBase) initComplianceRequirements() {
	kb.ComplianceRequirements["个人信息保护法"] = ComplianceReq{
		Regulation:  "个人信息保护法",
		Article:     "第四条",
		Description: "个人信息的处理应当遵循合法、正当、必要原则",
		Requirements: []string{
			"获得个人同意",
			"明确处理目的",
			"最小化处理原则",
		},
		Penalties: "违法处理个人信息的，最高可处五千万元以下罚款",
	}
}

// QueryThreatsBySystem 根据系统类型查询威胁
func (kb *PowerIndustryKnowledgeBase) QueryThreatsBySystem(systemType string) []ThreatInfo {
	var threats []ThreatInfo
	
	for _, threat := range kb.ThreatIntelligence {
		for _, target := range threat.TargetSystems {
			if target == systemType {
				threats = append(threats, threat)
				break
			}
		}
	}
	
	return threats
}

// QueryBusinessRules 查询业务规则
func (kb *PowerIndustryKnowledgeBase) QueryBusinessRules(systemType, category string) []BusinessRule {
	var rules []BusinessRule
	
	for _, rule := range kb.BusinessLogicRules {
		if rule.SystemType == systemType && rule.Category == category {
			rules = append(rules, rule)
		}
	}
	
	return rules
}

// QueryComplianceRequirements 查询合规要求
func (kb *PowerIndustryKnowledgeBase) QueryComplianceRequirements(regulation string) (ComplianceReq, bool) {
	req, exists := kb.ComplianceRequirements[regulation]
	return req, exists
}

// GenerateTestStrategy 生成测试策略
func (kb *PowerIndustryKnowledgeBase) GenerateTestStrategy(ctx context.Context, systemType string) (*TestStrategy, error) {
	strategy := &TestStrategy{
		SystemType: systemType,
		Threats:    kb.QueryThreatsBySystem(systemType),
		TestPriorities: make([]TestPriority, 0),
	}
	
	// 基于威胁严重程度确定测试优先级
	for _, threat := range strategy.Threats {
		priority := TestPriority{
			ThreatName: threat.Name,
			Priority:   kb.calculatePriority(threat.Severity, threat.Likelihood),
			TestMethods: kb.getTestMethodsForThreat(threat),
		}
		strategy.TestPriorities = append(strategy.TestPriorities, priority)
	}
	
	return strategy, nil
}

// calculatePriority 计算测试优先级
func (kb *PowerIndustryKnowledgeBase) calculatePriority(severity, likelihood string) int {
	severityScore := map[string]int{"低": 1, "中": 2, "高": 3, "极高": 4}
	likelihoodScore := map[string]int{"低": 1, "中": 2, "高": 3}
	
	return severityScore[severity] * likelihoodScore[likelihood]
}

// getTestMethodsForThreat 获取威胁对应的测试方法
func (kb *PowerIndustryKnowledgeBase) getTestMethodsForThreat(threat ThreatInfo) []string {
	// 基于威胁类型返回相应的测试方法
	methodMap := map[string][]string{
		"业务逻辑攻击": {"业务逻辑测试", "参数篡改测试", "状态转换测试"},
		"支付安全":    {"支付金额验证", "交易重放测试", "支付状态检查"},
		"注入攻击":    {"SQL注入测试", "NoSQL注入测试", "命令注入测试"},
	}
	
	if methods, exists := methodMap[threat.Category]; exists {
		return methods
	}
	
	return []string{"通用安全测试"}
}

// TestStrategy 测试策略
type TestStrategy struct {
	SystemType     string         `json:"system_type"`
	Threats        []ThreatInfo   `json:"threats"`
	TestPriorities []TestPriority `json:"test_priorities"`
}

// TestPriority 测试优先级
type TestPriority struct {
	ThreatName  string   `json:"threat_name"`
	Priority    int      `json:"priority"`
	TestMethods []string `json:"test_methods"`
}
