package database

import (
	"database/sql"
	"encoding/json"
	"time"
)

// PowerGridDeviceType 电力设备类型
type PowerGridDeviceType string

const (
	DeviceTypeSCADA       PowerGridDeviceType = "scada"
	DeviceTypeHMI         PowerGridDeviceType = "hmi"
	DeviceTypeRTU         PowerGridDeviceType = "rtu"
	DeviceTypePLC         PowerGridDeviceType = "plc"
	DeviceTypeProtection  PowerGridDeviceType = "protection"
	DeviceTypeTransformer PowerGridDeviceType = "transformer"
	DeviceTypeSwitch      PowerGridDeviceType = "switch"
)

// PowerGridProtocol 电力协议类型
type PowerGridProtocol string

const (
	ProtocolIEC61850 PowerGridProtocol = "iec61850"
	ProtocolDNP3     PowerGridProtocol = "dnp3"
	ProtocolModbus   PowerGridProtocol = "modbus"
	ProtocolIEC104   PowerGridProtocol = "iec104"
	ProtocolOPC      PowerGridProtocol = "opc"
)

// PowerGridVulnerabilityType 电力系统漏洞类型
type PowerGridVulnerabilityType string

const (
	VulnTypeProtocol     PowerGridVulnerabilityType = "protocol"
	VulnTypeDevice       PowerGridVulnerabilityType = "device"
	VulnTypeNetwork      PowerGridVulnerabilityType = "network"
	VulnTypeApplication  PowerGridVulnerabilityType = "application"
	VulnTypeConfiguration PowerGridVulnerabilityType = "configuration"
)

// PowerGridDevice 电力设备模型
type PowerGridDevice struct {
	ID           int64               `json:"id"`
	FlowID       int64               `json:"flow_id"`
	DeviceType   PowerGridDeviceType `json:"device_type"`
	Name         string              `json:"name"`
	IPAddress    string              `json:"ip_address"`
	MACAddress   sql.NullString      `json:"mac_address"`
	Manufacturer string              `json:"manufacturer"`
	Model        string              `json:"model"`
	Version      string              `json:"version"`
	Protocols    json.RawMessage     `json:"protocols"`
	Location     string              `json:"location"`
	Status       string              `json:"status"`
	Metadata     json.RawMessage     `json:"metadata"`
	CreatedAt    time.Time           `json:"created_at"`
	UpdatedAt    time.Time           `json:"updated_at"`
}

// PowerGridProtocolAnalysis 协议分析结果
type PowerGridProtocolAnalysis struct {
	ID           int64             `json:"id"`
	FlowID       int64             `json:"flow_id"`
	DeviceID     sql.NullInt64     `json:"device_id"`
	Protocol     PowerGridProtocol `json:"protocol"`
	SourceIP     string            `json:"source_ip"`
	DestIP       string            `json:"dest_ip"`
	Port         int               `json:"port"`
	PacketCount  int               `json:"packet_count"`
	DataSize     int64             `json:"data_size"`
	Encrypted    bool              `json:"encrypted"`
	Authenticated bool             `json:"authenticated"`
	Anomalies    json.RawMessage   `json:"anomalies"`
	RawData      json.RawMessage   `json:"raw_data"`
	CreatedAt    time.Time         `json:"created_at"`
}

// PowerGridVulnerability 电力系统漏洞
type PowerGridVulnerability struct {
	ID           int64                       `json:"id"`
	FlowID       int64                       `json:"flow_id"`
	DeviceID     sql.NullInt64               `json:"device_id"`
	VulnType     PowerGridVulnerabilityType  `json:"vuln_type"`
	CVE          sql.NullString              `json:"cve"`
	Title        string                      `json:"title"`
	Description  string                      `json:"description"`
	Severity     string                      `json:"severity"`
	CVSS         sql.NullFloat64             `json:"cvss"`
	Impact       string                      `json:"impact"`
	Solution     string                      `json:"solution"`
	References   json.RawMessage             `json:"references"`
	Exploitable  bool                        `json:"exploitable"`
	Verified     bool                        `json:"verified"`
	CreatedAt    time.Time                   `json:"created_at"`
}

// PowerGridCompliance 合规性检查结果
type PowerGridCompliance struct {
	ID           int64           `json:"id"`
	FlowID       int64           `json:"flow_id"`
	Standard     string          `json:"standard"`
	Category     string          `json:"category"`
	Requirement  string          `json:"requirement"`
	Status       string          `json:"status"`
	Score        sql.NullFloat64 `json:"score"`
	Evidence     json.RawMessage `json:"evidence"`
	Remediation  string          `json:"remediation"`
	CreatedAt    time.Time       `json:"created_at"`
}

// PowerGridThreatIntel 威胁情报
type PowerGridThreatIntel struct {
	ID          int64           `json:"id"`
	ThreatType  string          `json:"threat_type"`
	IOC         string          `json:"ioc"`
	IOCType     string          `json:"ioc_type"`
	Severity    string          `json:"severity"`
	Description string          `json:"description"`
	Source      string          `json:"source"`
	Tags        json.RawMessage `json:"tags"`
	FirstSeen   time.Time       `json:"first_seen"`
	LastSeen    time.Time       `json:"last_seen"`
	CreatedAt   time.Time       `json:"created_at"`
}

// PowerGridIncident 安全事件
type PowerGridIncident struct {
	ID          int64           `json:"id"`
	FlowID      int64           `json:"flow_id"`
	DeviceID    sql.NullInt64   `json:"device_id"`
	IncidentType string         `json:"incident_type"`
	Title       string          `json:"title"`
	Description string          `json:"description"`
	Severity    string          `json:"severity"`
	Status      string          `json:"status"`
	Timeline    json.RawMessage `json:"timeline"`
	Evidence    json.RawMessage `json:"evidence"`
	Response    json.RawMessage `json:"response"`
	CreatedAt   time.Time       `json:"created_at"`
	UpdatedAt   time.Time       `json:"updated_at"`
}
