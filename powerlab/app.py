#!/usr/bin/env python3
"""
PowerLab - 电力系统网络安全测试靶场
专为测试PowerSecAI系统的电力行业安全检测能力而设计
包含典型的电力系统网络架构和安全漏洞
"""

from flask import Flask, request, render_template_string, jsonify, send_file
import sqlite3
import socket
import struct
import threading
import time
import json
import os
from datetime import datetime

app = Flask(__name__)
app.secret_key = 'powerlab_secret_2025'

# 初始化数据库
def init_db():
    conn = sqlite3.connect('powerlab.db')
    cursor = conn.cursor()
    
    # 电力设备表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS power_devices (
            id INTEGER PRIMARY KEY,
            device_type TEXT,
            name TEXT,
            ip_address TEXT,
            protocol TEXT,
            manufacturer TEXT,
            model TEXT,
            location TEXT,
            status TEXT,
            last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 协议通信日志表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS protocol_logs (
            id INTEGER PRIMARY KEY,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            protocol TEXT,
            source_ip TEXT,
            dest_ip TEXT,
            message_type TEXT,
            data TEXT,
            security_level TEXT
        )
    ''')
    
    # 安全事件表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS security_events (
            id INTEGER PRIMARY KEY,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            event_type TEXT,
            severity TEXT,
            source_ip TEXT,
            target_device TEXT,
            description TEXT,
            status TEXT
        )
    ''')
    
    # 插入模拟电力设备数据
    devices = [
        (1, 'SCADA', '主调度中心', '************', 'IEC61850', 'ABB', 'System800xA', '调度中心', 'online'),
        (2, 'RTU', '变电站RTU-01', '************', 'DNP3', 'Schneider', 'ION7650', '220kV变电站', 'online'),
        (3, 'HMI', '操作员工作站', '************', 'Modbus', 'Siemens', 'WinCC', '控制室', 'online'),
        (4, 'Protection', '保护装置-01', '************', 'IEC61850', 'GE', 'D60', '主变保护', 'online'),
        (5, 'Gateway', '协议网关', '************', 'Multiple', 'Moxa', 'MGate5105', '通信机房', 'online'),
    ]
    
    cursor.executemany("INSERT OR REPLACE INTO power_devices VALUES (?,?,?,?,?,?,?,?,?,datetime('now'))", devices)
    
    conn.commit()
    conn.close()

# 主页 - 电力系统概览
@app.route('/')
def index():
    return render_template_string('''
    <!DOCTYPE html>
    <html>
    <head>
        <title>PowerLab - 电力系统安全测试靶场</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f0f8ff; }
            .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            h1 { color: #1e3a8a; text-align: center; }
            .nav { background: #1e40af; padding: 15px; margin: -30px -30px 30px -30px; border-radius: 10px 10px 0 0; }
            .nav a { color: white; text-decoration: none; margin-right: 20px; padding: 8px 15px; border-radius: 5px; }
            .nav a:hover { background: #1e3a8a; }
            .system-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 30px; }
            .system-card { background: #e0f2fe; padding: 20px; border-radius: 8px; border-left: 4px solid #0284c7; }
            .system-card h3 { margin-top: 0; color: #0c4a6e; }
            .vuln-indicator { background: #dc2626; color: white; padding: 3px 8px; border-radius: 3px; font-size: 12px; }
            .protocol-badge { background: #059669; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px; margin: 2px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="nav">
                <a href="/">系统概览</a>
                <a href="/scada">SCADA系统</a>
                <a href="/devices">设备管理</a>
                <a href="/protocols">协议监控</a>
                <a href="/security">安全中心</a>
                <a href="/api/devices">设备API</a>
            </div>
            
            <h1>⚡ PowerLab 电力系统安全测试靶场</h1>
            <p style="text-align: center; color: #64748b;">模拟真实电力企业网络环境，专为PowerSecAI安全检测系统设计</p>
            
            <div class="system-grid">
                <div class="system-card">
                    <h3>🏢 调度控制中心</h3>
                    <p>主SCADA系统 - IEC 61850协议</p>
                    <p><span class="protocol-badge">IEC61850</span> <span class="vuln-indicator">未加密通信</span></p>
                    <p>IP: ************</p>
                </div>
                
                <div class="system-card">
                    <h3>🔌 变电站RTU</h3>
                    <p>远程终端单元 - DNP3协议</p>
                    <p><span class="protocol-badge">DNP3</span> <span class="vuln-indicator">弱认证</span></p>
                    <p>IP: ************</p>
                </div>
                
                <div class="system-card">
                    <h3>🖥️ 操作员工作站</h3>
                    <p>人机界面系统 - Modbus协议</p>
                    <p><span class="protocol-badge">Modbus</span> <span class="vuln-indicator">无认证</span></p>
                    <p>IP: ************</p>
                </div>
                
                <div class="system-card">
                    <h3>🛡️ 保护装置</h3>
                    <p>继电保护设备 - IEC 61850</p>
                    <p><span class="protocol-badge">IEC61850</span> <span class="vuln-indicator">默认密码</span></p>
                    <p>IP: ************</p>
                </div>
                
                <div class="system-card">
                    <h3>🌐 协议网关</h3>
                    <p>多协议转换网关</p>
                    <p><span class="protocol-badge">Multiple</span> <span class="vuln-indicator">配置漏洞</span></p>
                    <p>IP: ************</p>
                </div>
                
                <div class="system-card">
                    <h3>📊 历史数据库</h3>
                    <p>实时数据库系统</p>
                    <p><span class="protocol-badge">SQL</span> <span class="vuln-indicator">SQL注入</span></p>
                    <p>IP: ************</p>
                </div>
            </div>
            
            <div style="margin-top: 40px; padding: 20px; background: #fef3c7; border-radius: 8px;">
                <h3>🎯 PowerSecAI 测试目标</h3>
                <p>PowerSecAI系统应该能够：</p>
                <ul>
                    <li>自动发现电力系统网络拓扑和设备</li>
                    <li>识别和分析电力工控协议通信</li>
                    <li>检测协议层面的安全漏洞</li>
                    <li>评估设备配置安全性</li>
                    <li>生成符合电力行业标准的安全报告</li>
                    <li>提供针对性的安全加固建议</li>
                </ul>
            </div>
        </div>
    </body>
    </html>
    ''')

# SCADA系统模拟
@app.route('/scada')
def scada_system():
    # 模拟SCADA系统登录漏洞
    return render_template_string('''
    <!DOCTYPE html>
    <html>
    <head><title>SCADA系统 - PowerLab</title></head>
    <body style="font-family: Arial; margin: 40px;">
        <h2>🏢 SCADA调度控制系统</h2>
        <div style="background: #fee2e2; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <strong>⚠️ 安全漏洞模拟</strong><br>
            - 默认管理员账户: admin/admin<br>
            - IEC 61850通信未加密<br>
            - 缺乏访问控制机制
        </div>
        
        <form method="post" action="/scada/login">
            <p>用户名: <input type="text" name="username" value="admin"></p>
            <p>密码: <input type="password" name="password" value="admin"></p>
            <p><input type="submit" value="登录系统"></p>
        </form>
        
        <h3>系统状态监控</h3>
        <div id="realtime-data">
            <p>主变压器负载: <span style="color: green;">85%</span></p>
            <p>线路电压: <span style="color: blue;">220kV</span></p>
            <p>系统频率: <span style="color: orange;">50.02Hz</span></p>
        </div>
        
        <a href="/">返回首页</a>
    </body>
    </html>
    ''')

@app.route('/scada/login', methods=['POST'])
def scada_login():
    username = request.form.get('username', '')
    password = request.form.get('password', '')
    
    # 模拟弱认证漏洞
    if username == 'admin' and password == 'admin':
        return '''
        <h1>🎉 SCADA系统登录成功!</h1>
        <p><strong>FLAG{scada_weak_authentication}</strong></p>
        <p>检测到安全问题：使用默认管理员凭据</p>
        <p>建议：更改默认密码，启用多因素认证</p>
        <a href="/scada">返回</a>
        '''
    else:
        return '<h1>登录失败</h1><a href="/scada">重试</a>'

# 设备管理接口 - 模拟协议漏洞
@app.route('/devices')
def device_management():
    conn = sqlite3.connect('powerlab.db')
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM power_devices")
    devices = cursor.fetchall()
    conn.close()
    
    device_html = ""
    for device in devices:
        device_html += f'''
        <tr>
            <td>{device[1]}</td>
            <td>{device[2]}</td>
            <td>{device[3]}</td>
            <td>{device[4]}</td>
            <td>{device[5]}</td>
            <td><span style="color: green;">{device[8]}</span></td>
            <td><a href="/device/{device[0]}">详情</a></td>
        </tr>
        '''
    
    return render_template_string(f'''
    <!DOCTYPE html>
    <html>
    <head><title>设备管理 - PowerLab</title></head>
    <body style="font-family: Arial; margin: 40px;">
        <h2>🔌 电力设备管理系统</h2>
        
        <table border="1" style="width: 100%; border-collapse: collapse;">
            <tr style="background: #f0f0f0;">
                <th>设备类型</th><th>设备名称</th><th>IP地址</th><th>协议</th><th>厂商</th><th>状态</th><th>操作</th>
            </tr>
            {device_html}
        </table>
        
        <div style="margin-top: 20px; background: #fef3c7; padding: 15px; border-radius: 5px;">
            <strong>🔍 协议安全检测点</strong><br>
            - IEC 61850 GOOSE消息未认证<br>
            - DNP3通信缺乏加密<br>
            - Modbus协议无访问控制<br>
            - 设备Web接口存在漏洞
        </div>
        
        <a href="/">返回首页</a>
    </body>
    </html>
    ''')

# 设备详情 - 模拟设备配置漏洞
@app.route('/device/<int:device_id>')
def device_detail(device_id):
    # 模拟不安全的直接对象引用
    conn = sqlite3.connect('powerlab.db')
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM power_devices WHERE id = ?", (device_id,))
    device = cursor.fetchone()
    conn.close()
    
    if device:
        config_data = {
            "device_id": device[0],
            "admin_password": "admin123",  # 暴露敏感信息
            "snmp_community": "public",
            "telnet_enabled": True,
            "encryption": False,
            "authentication": "none"
        }
        
        return f'''
        <h1>设备详细信息</h1>
        <p><strong>FLAG{{device_info_disclosure}}</strong></p>
        <h2>设备: {device[2]}</h2>
        <p>类型: {device[1]}</p>
        <p>IP: {device[3]}</p>
        <p>协议: {device[4]}</p>
        
        <h3>⚠️ 配置信息 (安全漏洞)</h3>
        <pre>{json.dumps(config_data, indent=2)}</pre>
        
        <p>检测到的安全问题：</p>
        <ul>
            <li>管理员密码过于简单</li>
            <li>SNMP使用默认团体名</li>
            <li>Telnet服务未加密</li>
            <li>缺乏身份认证机制</li>
        </ul>
        
        <a href="/devices">返回设备列表</a>
        '''
    else:
        return '<h1>设备不存在</h1><a href="/devices">返回</a>'

# API接口 - 模拟API安全漏洞
@app.route('/api/devices')
def api_devices():
    conn = sqlite3.connect('powerlab.db')
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM power_devices")
    devices = cursor.fetchall()
    conn.close()
    
    device_list = []
    for device in devices:
        device_list.append({
            'id': device[0],
            'type': device[1],
            'name': device[2],
            'ip': device[3],
            'protocol': device[4],
            'manufacturer': device[5],
            'model': device[6],
            'location': device[7],
            'status': device[8],
            # 敏感信息泄露
            'admin_credentials': {'username': 'admin', 'password': 'admin123'},
            'snmp_community': 'public',
            'internal_config': {'debug_mode': True, 'logging_level': 'verbose'}
        })
    
    return jsonify({
        'message': 'FLAG{api_information_disclosure}',
        'total_devices': len(device_list),
        'devices': device_list,
        'system_info': {
            'version': '1.0.0',
            'debug': True,
            'database_path': '/opt/powerlab/powerlab.db'
        }
    })

if __name__ == '__main__':
    init_db()
    app.run(host='0.0.0.0', port=5002, debug=True)
